package com.engine.minshuo.gyl.module.reportview.core.ext;

import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import weaver.hrm.User;

import java.util.*;

public class GetAnjianReportDataCoreExt {
    /**
     * 接口请求参数
     */
    protected Map<String, Object> params = null;
    /**
     * 操作人员
     */
    protected User user = null;
    /**
     * 响应结果
     */
    protected Map<String, Object> result = new HashMap<>();

    /**
     * 二开日志bean
     */
    protected SDLog sdLog = null;
    /**
     * 二开日志工具类
     */
    protected SDLogUtil sdLogUtil = null;
    /**
     * 错误信息
     */
    protected String error = "";
    /**
     * 批次数量
     */
    protected int batchSize = 1000;
    /**
     * 报告数据
     */
    protected List<Map<String, Object>> reportData = new ArrayList<>();
    /**
     * 评论数据
     */
    protected List<Map<String, Object>> commentData = new ArrayList<>();
    /**
     * 有权限的，且根据查询条件过滤后的数据id集合
     */
    protected Set<String> authIds = new HashSet<>();
}
