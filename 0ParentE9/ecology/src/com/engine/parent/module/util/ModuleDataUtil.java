package com.engine.parent.module.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.constant.SDThreadPoolCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.Modeviewlog;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.query.util.QueryUtil;
import com.weaver.formmodel.util.DateHelper;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.ThreadPoolUtil;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

/**
 * @FileName ModuleDataUtil
 * @Description 建模数据处理工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/14
 */
public class ModuleDataUtil {

    /**
     * 批量处理数据时，每批处理的数量
     */
    private static final int batchSize = 1000;

    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(ModuleDataUtil.class);
    /**
     * 表字段类型缓存
     * key: tableName_fieldName
     * value: FieldTypeCache
     */
    private static final Map<String, FieldTypeCache> fieldTypeCache = new ConcurrentHashMap<>();
    /**
     * 缓存清理任务
     */
    private static final ScheduledExecutorService cacheCleaner = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "field-type-cache-cleaner");
        t.setDaemon(true);
        return t;
    });

    static {
        // 启动定时清理任务，每5分钟执行一次
        cacheCleaner.scheduleAtFixedRate(() -> {
            try {
                cleanExpiredCache();
            } catch (Exception e) {
                log.error("Error cleaning expired cache", e);
            }
        }, 5, 5, TimeUnit.MINUTES);
    }

    /**
     * 清理过期的缓存项
     */
    private static void cleanExpiredCache() {
        int count = 0;
        Iterator<Map.Entry<String, FieldTypeCache>> it = fieldTypeCache.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, FieldTypeCache> entry = it.next();
            if (entry.getValue().isExpired()) {
                it.remove();
                count++;
            }
        }
        if (count > 0) {
            log.info("Cleaned " + count + " expired cache entries, remaining: " + fieldTypeCache.size());
        }
    }

    /**
     * 获取字段类型（优先从缓存获取）
     */
    private static String getFieldType(String tableName, String fieldName, String dbType) {
        String cacheKey = tableName.toUpperCase() + "_" + fieldName.toUpperCase();
        FieldTypeCache cache = fieldTypeCache.get(cacheKey);

        // 如果缓存存在且未过期，直接返回
        if (cache != null && !cache.isExpired()) {
            return cache.getDataType();
        }

        // 缓存不存在或已过期，从数据库查询
        String dataType = queryFieldTypeFromDB(tableName, fieldName, dbType);
        if (dataType != null) {
            // 更新缓存
            fieldTypeCache.put(cacheKey, new FieldTypeCache(dataType));
        }
        return dataType;
    }

    /**
     * 从数据库查询字段类型
     */
    private static String queryFieldTypeFromDB(String tableName, String fieldName, String dbType) {
        try {
            RecordSet rs = new RecordSet();
            String sql = "";
            switch (dbType.toLowerCase()) {
                case "mysql":
                    sql = "SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ? AND COLUMN_NAME = ?";
                    break;
                case "sqlserver":
                    sql = "SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ? AND COLUMN_NAME = ?";
                    break;
                case "oracle":
                    sql = "SELECT DATA_TYPE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? AND COLUMN_NAME = ?";
                    break;
                case "postgresql":
                    sql = "SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ? AND COLUMN_NAME = ?";
                    break;
                case "db2":
                    sql = "SELECT TYPENAME FROM SYSCAT.COLUMNS WHERE TABNAME = ? AND COLNAME = ?";
                    break;
                default:
                    return null;
            }

            if (rs.executeQuery(sql, tableName.toUpperCase(), fieldName.toUpperCase())) {
                if (rs.next()) {
                    return rs.getString(1).toLowerCase();
                }
            }
        } catch (Exception e) {
            log.error("Error querying field type for field " + fieldName + ", table: " + tableName, e);
        }
        return null;
    }

    /**
     * 同步插入建模数据(单条数据)
     * 有billid返回
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertOne(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insertOne--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2: 执行插入操作
                result = doInsertDataOne(insertBean);
                erroMsg = result.getErroMsg();
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insertOne--END ModuleResult：" + result);
        return result;
    }

    /**
     * 异步插入建模数据(单条数据)
     * 无billid返回
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertOneAc(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insertOneAc--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2:  创建ExecutorService线程池 异步执行操作
                getExecutor().execute(() -> {
                    //step 3: 执行插入操作
                    doInsertDataOne(insertBean);
                });
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insertOneAc--END ModuleResult：" + result);
        return result;
    }

    /**
     * 同步插入java对象
     *
     * @param javaObj
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObj(T javaObj,
                                             String tableName,
                                             int moduleId,
                                             int creator) {
        log.info("ModuleDataUtil-insertObj--START");
        ModuleResult mr = new ModuleResult();
        if (javaObj == null) {
            mr.setSuccess(false);
            mr.setErroMsg("javaObj缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleInsertBean mb = buildInsertObjBean(javaObj, tableName, moduleId, creator);
            // 异步插入单条数据
            mr = ModuleDataUtil.insertOne(mb);

        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObj--异常：", e);
        }
        log.info("ModuleDataUtil-insertObj--END");
        return mr;
    }

    /**
     * 同步插入java对象
     *
     * @param javaObj
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObj(T javaObj,
                                             String tableName,
                                             int moduleId,
                                             int creator,
                                             boolean needAuth) {
        log.info("ModuleDataUtil-insertObj--START");
        ModuleResult mr = new ModuleResult();
        if (javaObj == null) {
            mr.setSuccess(false);
            mr.setErroMsg("javaObj缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleInsertBean mb = buildInsertObjBean(javaObj, tableName, moduleId, creator);
            mb.setNeedRebuildAuth(needAuth);
            // 异步插入单条数据
            mr = ModuleDataUtil.insertOne(mb);

        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObj--异常：", e);
        }
        log.info("ModuleDataUtil-insertObj--END");
        return mr;
    }

    /**
     * 异步插入java对象
     *
     * @param javaObj
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjAC(T javaObj,
                                               String tableName,
                                               int moduleId,
                                               int creator) {
        log.info("ModuleDataUtil-insertObjAC--START");
        ModuleResult mr = new ModuleResult();
        if (javaObj == null) {
            mr.setSuccess(false);
            mr.setErroMsg("javaObj缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleInsertBean mb = buildInsertObjBean(javaObj, tableName, moduleId, creator);
            // 异步插入单条数据
            mr = ModuleDataUtil.insertOneAc(mb);

        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObjAC--异常：", e);
        }
        log.info("ModuleDataUtil-insertObjAC--END");
        return mr;
    }

    /**
     * 异步插入java对象
     *
     * @param javaObj
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjAC(T javaObj,
                                               String tableName,
                                               int moduleId,
                                               int creator,
                                               boolean needAuth) {
        log.info("ModuleDataUtil-insertObjAC--START");
        ModuleResult mr = new ModuleResult();
        if (javaObj == null) {
            mr.setSuccess(false);
            mr.setErroMsg("javaObj缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleInsertBean mb = buildInsertObjBean(javaObj, tableName, moduleId, creator);
            //设置是否需要数据权限
            mb.setNeedRebuildAuth(needAuth);
            // 异步插入单条数据
            mr = ModuleDataUtil.insertOneAc(mb);

        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObjAC--异常：", e);
        }
        log.info("ModuleDataUtil-insertObjAC--END");
        return mr;
    }

    /**
     * 构建对象插入的ModuleInsertBean
     *
     * @param javaObj
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     * @throws IllegalAccessException
     */
    private static <T> ModuleInsertBean buildInsertObjBean(T javaObj,
                                                           String tableName,
                                                           int moduleId,
                                                           int creator) throws IllegalAccessException {
        // 获取类信息
        Class<?> clazz = javaObj.getClass();
        List<String> insertFields = new ArrayList<>();
        List<Object> value = new ArrayList<>();

        // 获取字段和字段值
        Field[] fields = clazz.getDeclaredFields();
        // 组装插入字段列表，排除id字段
        for (Field field : fields) {
            if (!Modifier.isPublic(field.getModifiers())) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (!"id".equals(fieldName)) {
                    insertFields.add(fieldName);
                }
            }

        }
        // 组装所有插入数据，排除id字段
        for (Field field : fields) {
            if (!Modifier.isPublic(field.getModifiers())) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(javaObj);
                if (!"id".equals(fieldName)) {
                    value.add(fieldValue);
                }
            }
        }

        // 构造插入bean
        ModuleInsertBean mb = new ModuleInsertBean();
        mb.setTableName(tableName)
                .setFields(insertFields)
                .setValue(value)
                .setCreatorId(creator)
                .setModuleId(moduleId);
        return mb;
    }

    /**
     * 同步插入对象列表
     *
     * @param dataList
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjList(List<T> dataList,
                                                 String tableName,
                                                 int moduleId,
                                                 int creator) {
        log.info("ModuleDataUtil-insertObjList--START");
        ModuleResult mr = new ModuleResult();
        if (dataList == null || dataList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        ModuleInsertBean mb = buildObjListModuleInsertBean(dataList, tableName, moduleId, creator);
        if (mb != null) {
            // 同步插入批量数据
            mr = ModuleDataUtil.insert(mb);
        } else {
            mr.setSuccess(false);
            mr.setErroMsg("构建ModuleInsertBean失败");
        }
        log.info("ModuleDataUtil-insertObjList--END");
        return mr;
    }

    /**
     * 构建ObjList对应的 ModuleInsertBean
     *
     * @param dataList
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    private static <T> ModuleInsertBean buildObjListModuleInsertBean(List<T> dataList,
                                                                     String tableName,
                                                                     int moduleId,
                                                                     int creator) {
        ModuleInsertBean mb = null;
        try {
            // 获取类信息
            Class<?> clazz = dataList.get(0).getClass();
            List<String> insertFields = new ArrayList<>();
            List<List<Object>> values = new ArrayList<>();
            // 获取字段和字段值
            Field[] fields = clazz.getDeclaredFields();
            // 组装插入字段列表，排除id字段
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    if (!"id".equals(fieldName)) {
                        insertFields.add(fieldName);
                    }
                }

            }
            // 组装所有插入数据，排除id字段
            for (T data : dataList) {
                List<Object> value = new ArrayList<>();
                for (Field field : fields) {
                    if (!Modifier.isPublic(field.getModifiers())) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object fieldValue = field.get(data);
                        if (!"id".equals(fieldName)) {
                            value.add(fieldValue);
                        }
                    }
                }
                values.add(value);
            }

            // 构造插入bean
            mb = new ModuleInsertBean();
            mb.setTableName(tableName)
                    .setFields(insertFields)
                    .setValues(values)
                    .setCreatorId(creator)
                    .setModuleId(moduleId);
        } catch (Exception e) {
            log.error("buildObjListModuleInsertBean error", e);
        }
        return mb;
    }

    /**
     * 同步插入对象列表
     *
     * @param dataList
     * @param tableName
     * @param moduleId
     * @param creator
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjList(List<T> dataList,
                                                 String tableName,
                                                 int moduleId,
                                                 int creator,
                                                 boolean needRebuildAuth) {
        log.info("ModuleDataUtil-insertObjList--START");
        ModuleResult mr = new ModuleResult();
        if (dataList == null || dataList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        ModuleInsertBean mb = buildObjListModuleInsertBean(dataList, tableName, moduleId, creator);
        if (mb != null) {
            // 同步插入批量数据
            mb.setNeedRebuildAuth(needRebuildAuth);
            mr = ModuleDataUtil.insert(mb);
        } else {
            mr.setSuccess(false);
            mr.setErroMsg("构建ModuleInsertBean失败");
        }
        log.info("ModuleDataUtil-insertObjList--END");
        return mr;
    }

    /**
     * 异步插入一个建模主表的明细表数据 一条明细数据
     *
     * @param data
     * @param detailTableName
     * @param mainId
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjDetailOne(T data,
                                                      String detailTableName,
                                                      int mainId) {
        log.info("ModuleDataUtil-insertObjDetailAC--START");
        ModuleResult mr = new ModuleResult();
        if (data == null) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        // 获取类信息
        Class<?> clazz = data.getClass();
        List<String> insertFields = new ArrayList<>();
        List<List<Object>> values = new ArrayList<>();

        try {
            // 获取字段和字段值
            Field[] fields = clazz.getDeclaredFields();
            // 组装插入字段列表，排除id字段
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                        insertFields.add(fieldName);
                    }
                }
            }
            //最后添加mainid字段
            insertFields.add("mainid");
            // 组装所有插入数据，排除id字段

            List<Object> value = new ArrayList<>();
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    Object fieldValue = field.get(data);
                    if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                        value.add(fieldValue);
                    }
                }
            }
            //每行数据最后添加mainid
            value.add(mainId);
            values.add(value);

            // 构造插入bean
            ModuleInsertBean mb = new ModuleInsertBean();
            //组装插入的数据
            mb.setTableName(detailTableName)
                    .setFields(insertFields)
                    .setValues(values);
            log.info("插入计划明细数据detail mb:" + mb);
            mr = ModuleDataUtil.insertDetail(mb);
        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObjDetailAC--异常：", e);
        }
        log.info("ModuleDataUtil-insertObjDetailAC--END");
        return mr;
    }

    /**
     * 异步插入一个建模主表的明细表数据
     * 明细的mainid需要自行补充好
     *
     * @param dataList
     * @param detailTableName
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjDetailAC(List<T> dataList,
                                                     String detailTableName) {
        log.info("ModuleDataUtil-insertObjDetailAC--START");
        ModuleResult mr = new ModuleResult();
        if (dataList == null || dataList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        int eachMainId;
        // 获取类信息
        Class<?> clazz = dataList.get(0).getClass();
        List<String> insertFields = new ArrayList<>();
        List<List<Object>> values = new ArrayList<>();

        try {
            // 获取字段和字段值
            Field[] fields = clazz.getDeclaredFields();
            // 组装插入字段列表，排除id字段
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                        insertFields.add(fieldName);
                    }
                }

            }
            //insertFields最后添加mainid字段
            insertFields.add("mainid");
            // 组装所有插入数据，排除id和mainid字段
            for (T data : dataList) {
                eachMainId = -1;
                List<Object> value = new ArrayList<>();
                for (Field field : fields) {
                    if (!Modifier.isPublic(field.getModifiers())) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object fieldValue = field.get(data);
                        if ("mainid".equalsIgnoreCase(fieldName)) {
                            eachMainId = Util.getIntValue(Util.null2String(fieldValue), -1);
                        }
                        if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                            value.add(fieldValue);
                        }
                    }
                }
                if (eachMainId > 0) {
                    //每行数据最后添加mainid
                    value.add(eachMainId);
                    values.add(value);
                }
            }
            // 构造插入bean
            ModuleInsertBean mb = new ModuleInsertBean();
            //组装插入的数据
            mb.setTableName(detailTableName)
                    .setFields(insertFields)
                    .setValues(values);
            mr = ModuleDataUtil.insertDetailAc(mb);
        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.info("ModuleDataUtil-insertObjDetailAC--异常：", e);
        }
        log.info("ModuleDataUtil-insertObjDetailAC--END");
        return mr;
    }

    /**
     * 构建明细对象的插入bean
     *
     * @param dataList
     * @param detailTableName
     * @param mainId
     * @param <T>
     * @return
     */
    private static <T> ModuleInsertBean buildDetailObjectBean(List<T> dataList,
                                                              String detailTableName,
                                                              int mainId) {
        ModuleInsertBean mb = null;
        try {
            // 获取类信息
            Class<?> clazz = dataList.get(0).getClass();
            List<String> insertFields = new ArrayList<>();
            List<List<Object>> values = new ArrayList<>();
            // 获取字段和字段值
            Field[] fields = clazz.getDeclaredFields();
            // 组装插入字段列表，排除id字段
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                        insertFields.add(fieldName);
                    }
                }

            }
            //最后添加mainid字段
            insertFields.add("mainid");
            // 组装所有插入数据，排除id字段
            for (T data : dataList) {
                List<Object> value = new ArrayList<>();
                for (Field field : fields) {
                    if (!Modifier.isPublic(field.getModifiers())) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        Object fieldValue = field.get(data);
                        if (!"id".equalsIgnoreCase(fieldName) && !"mainid".equalsIgnoreCase(fieldName)) {
                            value.add(fieldValue);
                        }
                    }

                }
                //每行数据最后添加mainid
                value.add(mainId);
                values.add(value);
            }
            // 构造插入bean
            mb = new ModuleInsertBean();
            //组装插入的数据
            mb.setTableName(detailTableName)
                    .setFields(insertFields)
                    .setValues(values);
        } catch (Exception e) {
            log.error("buildDetailObjectBean error", e);
        }
        return mb;

    }

    /**
     * 同步插入1条建模主表数据的明细表数据
     *
     * @param dataList
     * @param detailTableName
     * @param mainId
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjDetail(List<T> dataList,
                                                   String detailTableName,
                                                   int mainId) {
        log.info("ModuleDataUtil-insertObjDetail--START");
        ModuleResult mr = new ModuleResult();
        if (dataList == null || dataList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        ModuleInsertBean mb = buildDetailObjectBean(dataList, detailTableName, mainId);
        if (mb != null) {
            mr = ModuleDataUtil.insertDetail(mb);
        } else {
            mr.setSuccess(false);
            mr.setErroMsg("构建ModuleInsertBean失败");
        }
        log.info("ModuleDataUtil-insertObjDetail--END");
        return mr;
    }

    /**
     * 异步插入一个建模主表的明细表数据
     *
     * @param dataList
     * @param detailTableName
     * @param mainId
     * @param <T>
     * @return
     */
    public static <T> ModuleResult insertObjDetailAC(List<T> dataList,
                                                     String detailTableName,
                                                     int mainId) {
        log.info("ModuleDataUtil-insertObjDetailAC--START");
        ModuleResult mr = new ModuleResult();
        if (dataList == null || dataList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("dataList缺失");
            return mr;
        }
        ModuleInsertBean mb = buildDetailObjectBean(dataList, detailTableName, mainId);
        if (mb != null) {
            mr = ModuleDataUtil.insertDetailAc(mb);
        } else {
            mr.setSuccess(false);
            mr.setErroMsg("构建ModuleInsertBean失败");
        }
        log.info("ModuleDataUtil-insertObjDetailAC--END");
        return mr;
    }

    /**
     * 同步更新java对象
     *
     * @param javaObj
     * @param tableName
     * @param updater
     * @param <T>
     * @return
     */
    public static <T> ModuleResult updateObj(T javaObj,
                                             String tableName,
                                             int billid,
                                             int updater) {
        log.info("ModuleDataUtil-updateObj--START");
        ModuleResult mr = new ModuleResult();
        if (javaObj == null) {
            mr.setSuccess(false);
            mr.setErroMsg("javaObj缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleUpdateBean moduleUpdateBean = buildUpdateObjBean(javaObj, tableName, billid, updater);
            List<List<Object>> values = new ArrayList<>();
            values.add(moduleUpdateBean.getValue());
            moduleUpdateBean.setValues(values);
            // 同步更新单条数据
            mr = ModuleDataUtil.update(moduleUpdateBean);
        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.error("ModuleDataUtil-updateObj--异常：", e);
        }
        log.info("ModuleDataUtil-updateObj--END");
        return mr;
    }

    /**
     * 同步更新建模主表列表
     *
     * @param objList
     * @param tableName
     * @param updater
     * @param <T>
     * @return
     */
    public static <T> ModuleResult updateObjList(List<T> objList,
                                                 String tableName,
                                                 int updater) {
        log.info("ModuleDataUtil-updateObj--START");
        ModuleResult mr = new ModuleResult();
        if (objList == null || objList.isEmpty()) {
            mr.setSuccess(false);
            mr.setErroMsg("objList缺失");
            return mr;
        }
        try {
            // 构造插入bean
            ModuleUpdateBean moduleUpdateBean = buildUpdateObjListBean(objList, tableName, updater);
            // 同步更新单条数据
            mr = ModuleDataUtil.update(moduleUpdateBean);
        } catch (Exception e) {
            mr.setSuccess(false);
            mr.setErroMsg("异常:" + e.getMessage());
            log.error("ModuleDataUtil-updateObj--异常：", e);
        }
        log.info("ModuleDataUtil-updateObj--END");
        return mr;
    }

    /**
     * 构建对象插入的ModuleInsertBean
     *
     * @param javaObj
     * @param tableName
     * @param updater
     * @param <T>
     * @return
     * @throws IllegalAccessException
     */
    private static <T> ModuleUpdateBean buildUpdateObjBean(T javaObj,
                                                           String tableName,
                                                           int billid,
                                                           int updater) throws IllegalAccessException {
        // 获取类信息
        Class<?> clazz = javaObj.getClass();
        List<String> updateFields = new ArrayList<>();
        List<Object> value = new ArrayList<>();
        // 获取字段和字段值
        Field[] fields = clazz.getDeclaredFields();
        // 组装插入字段列表，排除id字段
        for (Field field : fields) {
            if (!Modifier.isPublic(field.getModifiers())) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (!"id".equals(fieldName)) {
                    updateFields.add(fieldName);
                }
            }

        }

        // 组装所有更新数据，排除id字段
        for (Field field : fields) {
            if (!Modifier.isPublic(field.getModifiers())) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(javaObj);
                if (!"id".equals(fieldName)) {
                    value.add(fieldValue);
                }
            }
        }
        //id加在list最后一位上
        value.add(billid);

        //构造更新bena
        ModuleUpdateBean mb = new ModuleUpdateBean();
        mb.setFields(updateFields);
        mb.setTableName(tableName);
        mb.setValue(value);
        mb.setUpdater(updater);
        return mb;
    }

    /**
     * 构建对象插入的ModuleInsertBean
     *
     * @param objList
     * @param tableName
     * @param updater
     * @param <T>
     * @return
     * @throws IllegalAccessException
     */
    private static <T> ModuleUpdateBean buildUpdateObjListBean(List<T> objList,
                                                               String tableName,
                                                               int updater) throws IllegalAccessException {
        // 获取类信息
        Class<?> clazz = objList.get(0).getClass();
        List<String> updateFields = new ArrayList<>();
        List<List<Object>> values = new ArrayList<>();
        // 获取字段和字段值
        Field[] fields = clazz.getDeclaredFields();
        // 组装插入字段列表，排除id字段
        for (Field field : fields) {
            if (!Modifier.isPublic(field.getModifiers())) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (!"id".equals(fieldName)) {
                    updateFields.add(fieldName);
                }
            }
        }

        for (T data : objList) {
            List<Object> value = new ArrayList<>();
            Object idValue = null;
            for (Field field : fields) {
                if (!Modifier.isPublic(field.getModifiers())) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    Object fieldValue = field.get(data);
                    if (!"id".equalsIgnoreCase(fieldName)) {
                        value.add(fieldValue);
                    } else {
                        idValue = fieldValue;
                    }
                }
            }
            //最后再加上id字段
            value.add(idValue);
            values.add(value);
        }

        //构造更新bena
        ModuleUpdateBean mb = new ModuleUpdateBean();
        mb.setFields(updateFields);
        mb.setTableName(tableName);
        mb.setValues(values);
        mb.setUpdater(updater);
        return mb;
    }

    /**
     * 同步插入建模数据(批量数据)
     * 无billid返回
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insert(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insert--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2: 执行插入操作
                erroMsg = doInsertData(insertBean);
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insert--END ModuleResult：" + result);
        return result;
    }

    /**
     * 同步插入建模数据(批量数据)
     * 无billid返回
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertByOne(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insert--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2: 执行插入操作
                erroMsg = doInsertDataByOne(insertBean);
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insert--END ModuleResult：" + result);
        return result;
    }

    /**
     * 异步插入建模数据(批量数据)
     * 无billid返回
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertAc(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insertAc--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2:  创建ExecutorService线程池 异步执行操作
                getExecutor().execute(() -> {
                    //step 3: 执行插入操作
                    doInsertData(insertBean);
                });
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insertAc--END ModuleResult：" + result);
        return result;
    }

    /**
     * 同步插入建模明细数据（可批量）
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertDetail(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insertDetail--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkDetailParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2: 执行插入操作
                erroMsg = doInsertDetailData(insertBean);
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insertDetail--END ModuleResult：" + result);
        return result;
    }

    /**
     * 异步插入建模数据(批量数据)
     *
     * @param insertBean 建模插入bean
     * @return
     */
    public static ModuleResult insertDetailAc(ModuleInsertBean insertBean) {
        log.info("ModuleDataUtil-insertDetailAc--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkDetailParam(insertBean);
            if (erroMsg.isEmpty()) {
                //step 2:  创建ExecutorService线程池 异步执行操作
                getExecutor().execute(() -> {
                    //step 3: 执行插入操作
                    doInsertDetailData(insertBean);
                });
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-insertDetailAc--END ModuleResult：" + result);
        return result;
    }

    /**
     * 同步更新建模数据(批量数据)
     *
     * @param bean 建模更新bean
     * @return
     */
    public static ModuleResult update(ModuleUpdateBean bean) {
        log.info("ModuleDataUtil-update--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkUpdateParam(bean);
            if (erroMsg.isEmpty()) {
                //step 3: 执行更新操作
                erroMsg = doUpdateData(bean);
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-update--END ModuleResult：" + result);
        return result;
    }

    /**
     * 异步更新建模数据(批量数据)
     *
     * @param bean 建模更新bean
     * @return
     */
    public static ModuleResult updateAc(ModuleUpdateBean bean) {
        log.info("ModuleDataUtil-updateAc--START");
        ModuleResult result = new ModuleResult();
        String erroMsg;
        try {
            //step 1: 检查参数
            erroMsg = checkUpdateParam(bean);
            if (erroMsg.isEmpty()) {
                //step 2:  创建ExecutorService线程池 异步执行操作
                getExecutor().execute(() -> {
                    //step 3: 执行更新操作
                    doUpdateData(bean);
                });
            }
        } catch (Exception e) {
            log.info(SDUtil.getExceptionDetail(e));
            erroMsg = SDUtil.getExceptionDetail(e);
        }
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        log.info("ModuleDataUtil-updateAc--END ModuleResult：" + result);
        return result;
    }

    /**
     * 校验参数
     *
     * @param insertBean
     * @return
     */
    public static String checkParam(ModuleInsertBean insertBean) {
        StringBuilder erroMsg = new StringBuilder();
        //校验1: 参数必填
        if (insertBean.getFields() == null || insertBean.getFields().isEmpty()) {
            erroMsg.append("<insertFields> is empty").append(";");
        }
        if (StringUtils.isBlank(insertBean.getTableName())) {
            erroMsg.append("<tableName> is empty").append(";");
        }
        if (insertBean.getValues() == null && insertBean.getValue() == null) {
            erroMsg.append("<value> or <values> is null").append(";");
        }
        if (insertBean.getValues() != null && insertBean.getValues().isEmpty()) {
            erroMsg.append("<values> is empty").append(";");
        }
        if (insertBean.getModuleId() <= 0) {
            erroMsg.append("moduleId:").append(insertBean.getModuleId()).append(" is wrong").append(";");
        }
        if (erroMsg.toString().isEmpty()) {
            //校验2: 插入的字段和值，数量是否一致
            if (insertBean.getValues() != null) {
                if (insertBean.getFields().size() != insertBean.getValues().get(0).size()) {
                    erroMsg.append("<insertFields> has different size with <values> ").append(";");
                }
            }
            if (insertBean.getValue() != null) {
                if (insertBean.getFields().size() != insertBean.getValue().size()) {
                    erroMsg.append("<insertFields> has different size with <value> ").append(";");
                }
            }
        }
        if (!erroMsg.toString().isEmpty()) {
            erroMsg = new StringBuilder(erroMsg.substring(0, erroMsg.length() - 1));
        }
        return erroMsg.toString();
    }

    /**
     * 校验明细表参数
     *
     * @param insertBean
     * @return
     */
    private static String checkDetailParam(ModuleInsertBean insertBean) {
        StringBuilder erroMsg = new StringBuilder();
        //校验1: 参数必填
        if (insertBean.getFields() == null || insertBean.getFields().isEmpty()) {
            erroMsg.append("<insertFields> is empty").append(";");
        }
        if (StringUtils.isBlank(insertBean.getTableName())) {
            erroMsg.append("<tableName> is empty").append(";");
        }
        if (insertBean.getValues() == null && insertBean.getValue() == null) {
            erroMsg.append("<value> or <values> is null").append(";");
        }
        if (insertBean.getValues() != null && insertBean.getValues().isEmpty()) {
            erroMsg.append("<values> is empty").append(";");
        }
        if (erroMsg.toString().isEmpty()) {
            //校验2: 插入的字段和值，数量是否一致
            if (insertBean.getValues() != null) {
                if (insertBean.getFields().size() != insertBean.getValues().get(0).size()) {
                    erroMsg.append("<insertFields> has different size with <values> ").append(";");
                }
            }
            if (insertBean.getValue() != null) {
                if (insertBean.getFields().size() != insertBean.getValue().size()) {
                    erroMsg.append("<insertFields> has different size with <value> ").append(";");
                }
            }
        }
        if (!erroMsg.toString().isEmpty()) {
            erroMsg = new StringBuilder(erroMsg.substring(0, erroMsg.length() - 1));
        }
        return erroMsg.toString();
    }

    /**
     * 校验更新数据的参数
     *
     * @param bean
     * @return
     */
    private static String checkUpdateParam(ModuleUpdateBean bean) {
        StringBuilder erroMsg = new StringBuilder();
        //校验1: 参数必填
        if (bean.getFields() == null || bean.getFields().isEmpty()) {
            erroMsg.append("<updateFields> is empty").append(";");
        }
        if (StringUtils.isBlank(bean.getTableName())) {
            erroMsg.append("<tableName> is empty").append(";");
        }
        if (bean.getValues() == null && bean.getValue() == null) {
            erroMsg.append("<value> or <values> is null").append(";");
        }
        if (bean.getValues() != null && bean.getValues().isEmpty()) {
            erroMsg.append("<values> is empty").append(";");
        }

        //校验2:更新的字段不能有id字段
        if (checkContainsStr(bean.getFields(), "id")) {
            erroMsg.append("<updateFields> can not have id column").append(";");
        }
        if (erroMsg.toString().isEmpty()) {
            //校验2: 更新字段=更新值列表-1，更新值列表最后一个值为id值
            if (bean.getValues() != null) {
                if (bean.getFields().size() != bean.getValues().get(0).size() - 1) {
                    erroMsg.append("<updateFields> does not match  <values> ").append(";");
                }
            }
            if (bean.getValue() != null) {
                if (bean.getFields().size() != bean.getValue().size() - 1) {
                    erroMsg.append("<updateFields> does not match  <value> ").append(";");
                }
            }
        }
        if (!erroMsg.toString().isEmpty()) {
            erroMsg = new StringBuilder(erroMsg.substring(0, erroMsg.length() - 1));
        }
        return erroMsg.toString();
    }

    /**
     * 实际执行插入建模主表数据(批量)，无billid返回
     *
     * @param insertBean
     * @return
     */
    @SuppressWarnings("unchecked")
    private static String doInsertData(ModuleInsertBean insertBean) {
        String erroMsg = "";
        String sql;
        String beginTime = TimeUtil.getCurrentTimeString();
        log.info("doInsertData--START time:" + beginTime);
        log.info("moduleId：" + insertBean.getModuleId());
        int creator = insertBean.getCreatorId();
        String currentDate = DateHelper.getCurrentDate();
        String currentTime = DateHelper.getCurrentTime();
        StringBuilder sbFields = new StringBuilder();
        //所有记录使用记录SQL操作的参数集合
        List paramList;
        //每一个记录使用记录SQL操作的参数集合
        List<Object> eachParam;
        StringBuilder sbValues = new StringBuilder();

        //step 1: 拼接insert语句，value值用?占位
        List<String> insertFields = insertBean.getFields();
        for (int i = 0; i < insertFields.size(); i++) {
            sbFields.append(insertFields.get(i));
            sbValues.append("?");
            if (i < insertFields.size() - 1) {
                sbFields.append(",");
                sbValues.append(",");
            }
        }

        //step 2:拼接formmodeid,modedatacreater,modedatacreatedate,modedatacreatetime
        if (!checkContainsStr(insertFields, "formmodeid")) {
            sbFields.append(",formmodeid");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreater")) {
            sbFields.append(",modedatacreater");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreatedate")) {
            sbFields.append(",modedatacreatedate");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreatetime")) {
            sbFields.append(",modedatacreatetime");
            sbValues.append(",?");
        }

        log.info("insertFields：" + sbFields);
        // step 3 : 定义插入数据的sql
        sql = "insert into " + insertBean.getTableName() + "(" + sbFields + ") values (" + sbValues + ")";
        log.info("insert module sql:" + sql);

        //step 4 : 处理列表值，按批次处理，1000条一批
        List<List<Object>> values = insertBean.getValues();
        // 原始列表的长度
        int totalSize = values.size();
        log.info("totalSize :" + totalSize);
        // 分批处理的数量
        int numBatches = (int) Math.ceil((double) totalSize / batchSize);
        log.info("batch number size:" + numBatches);

        // 获取数据库类型
        RecordSet rs = new RecordSet();
        String dbType = rs.getDBType();

        for (int i = 0; i < numBatches; i++) {
            // 计算每个批次的起始索引和结束索引
            int startIndex = i * batchSize;
            int endIndex = Math.min((i + 1) * batchSize, totalSize);
            // 获取当前批次的value列表
            List<List<Object>> batchList = values.subList(startIndex, endIndex);
            log.info("current batch :" + i + ",batchList size ：" + batchList.size());
            //每个批次重置参数列表
            paramList = new ArrayList<>();

            // step 5 : 添加建模主表必要的字段值
            for (List<Object> eachValue : batchList) {
                eachParam = new ArrayList<>();
                // 转换字段类型
                for (int j = 0; j < eachValue.size(); j++) {
                    Object convertedValue = convertValueByFieldType(eachValue.get(j), insertFields.get(j), insertBean.getTableName(), dbType);
                    eachParam.add(convertedValue);
                }

                if (!checkContainsStr(insertFields, "formmodeid")) {
                    eachParam.add(insertBean.getModuleId());
                }
                if (!checkContainsStr(insertFields, "modedatacreater")) {
                    eachParam.add(creator);
                }
                if (!checkContainsStr(insertFields, "modedatacreatedate")) {
                    eachParam.add(currentDate);
                }
                if (!checkContainsStr(insertFields, "modedatacreatetime")) {
                    eachParam.add(currentTime);
                }
                paramList.add(eachParam);
            }
            log.info("batch insert first :" + paramList.get(0));
            //step 6: 执行sql，批量插入数据
            if (!rs.executeBatchSql(sql, paramList)) {
                erroMsg = "current batch :" + i + "，batch insert sql error:";
                if (!insertBean.isE8()) {
                    erroMsg += rs.getExceptionMsg();
                }
                log.info(erroMsg);
            }
        }
        if (erroMsg.isEmpty()) {
            //step 7 : 所有数据处理完后，重构建模所有权限
            //增加判断，是否需要重构权限
            if (insertBean.isNeedRebuildAuth()) {
                List<String> newBillIds = getNewBillIds(currentDate, currentTime, insertBean.getTableName());
                //优化，只重构在插入的创建日期之后的数据
                resetModShare(insertBean.getModuleId(), newBillIds);
            }

        } else {
            log.info("insert module error：" + erroMsg);
        }
        String endTime = TimeUtil.getCurrentTimeString();
        //计算开始到结束时间相隔的秒数
        long takeTime = TimeUtil.timeInterval(beginTime, endTime);
        log.info("doInsertData---END time:" + endTime + ",it takes--" + takeTime + "--second");
        return erroMsg;
    }

    /**
     * 实际执行插入建模主表数据(批量)，无billid返回
     * 低版本权限重构要一条条来
     *
     * @param insertBean
     * @return
     */
    private static String doInsertDataByOne(ModuleInsertBean insertBean) {
        String erroMsg = "";
        String sql;
        String beginTime = TimeUtil.getCurrentTimeString();
        log.info("doInsertData--START time:" + beginTime);
        log.info("moduleId：" + insertBean.getModuleId());
        int creator = insertBean.getCreatorId();
        String currentDate = DateHelper.getCurrentDate();
        String currentTime = DateHelper.getCurrentTime();
        StringBuilder sbFields = new StringBuilder();
        //所有记录使用记录SQL操作的参数集合
        List paramList;
        //每一个记录使用记录SQL操作的参数集合
        List<Object> eachParam;
        StringBuilder sbValues = new StringBuilder();

        //step 1: 拼接insert语句，value值用?占位
        List<String> insertFields = insertBean.getFields();
        for (int i = 0; i < insertFields.size(); i++) {
            sbFields.append(insertFields.get(i));
            sbValues.append("?");
            if (i < insertFields.size() - 1) {
                sbFields.append(",");
                sbValues.append(",");
            }
        }

        //step 2:拼接formmodeid,modedatacreater,modedatacreatedate,modedatacreatetime
        if (!checkContainsStr(insertFields, "formmodeid")) {
            sbFields.append(",formmodeid");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreater")) {
            sbFields.append(",modedatacreater");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreatedate")) {
            sbFields.append(",modedatacreatedate");
            sbValues.append(",?");
        }
        if (!checkContainsStr(insertFields, "modedatacreatetime")) {
            sbFields.append(",modedatacreatetime");
            sbValues.append(",?");
        }

        log.info("insertFields：" + sbFields);
        // step 3 : 定义插入数据的sql
        sql = "insert into " + insertBean.getTableName() + "(" + sbFields + ") values (" + sbValues + ")";
        log.info("insert module sql:" + sql);

        //step 4 : 处理列表值，按批次处理，1000条一批
        List<List<Object>> values = insertBean.getValues();
        // 原始列表的长度
        int totalSize = values.size();
        log.info("totalSize :" + totalSize);
        // 分批处理的数量
        int numBatches = (int) Math.ceil((double) totalSize / batchSize);
        log.info("batch number size:" + numBatches);

        // 获取数据库类型
        RecordSet rs = new RecordSet();
        String dbType = rs.getDBType();

        for (int i = 0; i < numBatches; i++) {
            // 计算每个批次的起始索引和结束索引
            int startIndex = i * batchSize;
            int endIndex = Math.min((i + 1) * batchSize, totalSize);
            // 获取当前批次的value列表
            List<List<Object>> batchList = values.subList(startIndex, endIndex);
            log.info("current batch :" + i + ",batchList size ：" + batchList.size());
            //每个批次重置参数列表
            paramList = new ArrayList<>();

            // step 5 : 添加建模主表必要的字段值
            for (List<Object> eachValue : batchList) {
                eachParam = new ArrayList<>();
                // 转换字段类型
                for (int j = 0; j < eachValue.size(); j++) {
                    Object convertedValue = convertValueByFieldType(eachValue.get(j), insertFields.get(j), insertBean.getTableName(), dbType);
                    eachParam.add(convertedValue);
                }

                if (!checkContainsStr(insertFields, "formmodeid")) {
                    eachParam.add(insertBean.getModuleId());
                }
                if (!checkContainsStr(insertFields, "modedatacreater")) {
                    eachParam.add(creator);
                }
                if (!checkContainsStr(insertFields, "modedatacreatedate")) {
                    eachParam.add(currentDate);
                }
                if (!checkContainsStr(insertFields, "modedatacreatetime")) {
                    eachParam.add(currentTime);
                }
                paramList.add(eachParam);
            }
            log.info("batch insert first :" + paramList.get(0));
            //step 6: 执行sql，批量插入数据
            if (!rs.executeBatchSql(sql, paramList)) {
                erroMsg = "current batch :" + i + "，batch insert sql error:";
                if (!insertBean.isE8()) {
                    erroMsg += rs.getExceptionMsg();
                }
                log.info(erroMsg);
            }
        }
        if (erroMsg.isEmpty()) {
            //step 7 : 所有数据处理完后，重构建模所有权限
            //增加判断，是否需要重构权限
            if (insertBean.isNeedRebuildAuth()) {
                List<String> newBillIds = getNewBillIds(currentDate, currentTime, insertBean.getTableName());
                //优化，只重构在插入的创建日期之后的数据
                resetModShareOneByOne(insertBean.getModuleId(), newBillIds);
            }

        } else {
            log.info("insert module error：" + erroMsg);
        }
        String endTime = TimeUtil.getCurrentTimeString();
        //计算开始到结束时间相隔的秒数
        long takeTime = TimeUtil.timeInterval(beginTime, endTime);
        log.info("doInsertData---END time:" + endTime + ",it takes--" + takeTime + "--second");
        return erroMsg;
    }

    /**
     * 实际执行插入建模明细表数据(批量)，无billid返回
     *
     * @param insertBean
     * @return
     */
    private static String doInsertDetailData(ModuleInsertBean insertBean) {
        String erroMsg = "";
        String sql;
        String beginTime = TimeUtil.getCurrentTimeString();
        log.info("doInsertDetailData--START time:" + beginTime);
        log.info("moduleId：" + insertBean.getModuleId());
        StringBuilder sbFields = new StringBuilder();
        //所有记录使用记录SQL操作的参数集合
        List paramList;
        StringBuilder sbValues = new StringBuilder();

        //step 1: 拼接insert语句，value值用?占位
        List<String> insertFields = insertBean.getFields();
        for (int i = 0; i < insertFields.size(); i++) {
            sbFields.append(insertFields.get(i));
            sbValues.append("?");
            if (i < insertFields.size() - 1) {
                sbFields.append(",");
                sbValues.append(",");
            }
        }
        log.info("insertFields：" + sbFields);

        // step 2 : 定义插入数据的sql
        sql = "insert into " + insertBean.getTableName() + "(" + sbFields + ") values (" + sbValues + ")";
        log.info("insert module sql:" + sql);

        //step 3 : 处理列表值，按批次处理，1000条一批
        List<List<Object>> values = insertBean.getValues();
        // 原始列表的长度
        int totalSize = values.size();
        log.info("totalSize :" + totalSize);
        // 分批处理的数量
        int numBatches = (int) Math.ceil((double) totalSize / batchSize);
        log.info("batch number size:" + numBatches);

        // 获取数据库类型
        RecordSet rs = new RecordSet();
        String dbType = rs.getDBType();

        //step 4 :遍历批次
        for (int i = 0; i < numBatches; i++) {
            // 计算每个批次的起始索引和结束索引
            int startIndex = i * batchSize;
            int endIndex = Math.min((i + 1) * batchSize, totalSize);
            // 获取当前批次的value列表
            List<List<Object>> batchList = values.subList(startIndex, endIndex);
            log.info("current batch :" + i + ",batchList size ：" + batchList.size());
            // step 5 :每个批次重置参数列表
            paramList = new ArrayList<>();

            // 转换字段类型并添加到参数列表
            for (List<Object> eachValue : batchList) {
                List<Object> convertedValues = new ArrayList<>();
                for (int j = 0; j < eachValue.size(); j++) {
                    Object convertedValue = convertValueByFieldType(eachValue.get(j), insertFields.get(j), insertBean.getTableName(), dbType);
                    convertedValues.add(convertedValue);
                }
                paramList.add(convertedValues);
            }

            //step 6: 执行sql，批量插入数据
            if (!rs.executeBatchSql(sql, paramList)) {
                erroMsg = "current batch :" + i + "，batch insert sql error:";
                if (!insertBean.isE8()) {
                    erroMsg += rs.getExceptionMsg();
                }
                log.info("insert module error：" + erroMsg);
            }
        }
        String endTime = TimeUtil.getCurrentTimeString();
        //计算开始到结束时间相隔的秒数
        long takeTime = TimeUtil.timeInterval(beginTime, endTime);
        log.info("doInsertDetailData---END time:" + endTime + ",it takes--" + takeTime + "--second");
        return erroMsg;
    }

    /**
     * 实际执行插入建模主表数据(单条)，有billid返回
     *
     * @param insertBean
     * @return
     */
    public static ModuleResult doInsertDataOne(ModuleInsertBean insertBean) {
        ModuleResult result = new ModuleResult();
        String erroMsg = "";
        String beginTime = TimeUtil.getCurrentTimeString();
        log.info("doInsertDataOne--START time:" + beginTime);
        log.info("moduleId：" + insertBean.getModuleId());
        int creator = insertBean.getCreatorId();
        List<String> insertFields = insertBean.getFields();
        List<Object> value = insertBean.getValue();
        User user = new User(creator);
        StringBuilder sb = new StringBuilder();
        String currentDate = DateHelper.getCurrentDate();
        String currentTime = DateHelper.getCurrentTime();

        // 获取数据库类型
        RecordSet rs = new RecordSet();
        String dbType = rs.getDBType();

        // 转换值类型
        List<Object> convertedValues = new ArrayList<>();
        for (int i = 0; i < value.size(); i++) {
            Object convertedValue = convertValueByFieldType(value.get(i), insertFields.get(i), insertBean.getTableName(), dbType);
            convertedValues.add(convertedValue);
        }

        //插入数据后的billid
        int billid;
        //step 1: 先插入空的数据得到billid
        billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                insertBean.getTableName(),
                insertBean.getModuleId(),
                creator,
                (user.getLogintype()).equals("1") ? 0 : 1,
                currentDate,
                currentTime);
        log.info("generate new billid :" + billid);

        //step 2: 拼接sql占位符?
        for (int i = 0; i < insertFields.size(); i++) {
            sb.append(insertFields.get(i)).append("=").append("?");
            if (i != insertFields.size() - 1) {
                sb.append(", ");
            }
        }

        //step 3:更新业务字段值
        if (!rs.executeUpdate("update " + insertBean.getTableName() + " set " + sb + " where id=" + billid, convertedValues)) {
            //E9 RecordSet的报错使用 getExceptionMsg，getMsg没值,E8报错信息啥也没有，不能获取
            if (!insertBean.isE8()) {
                erroMsg = "update error:" + rs.getExceptionMsg();
            } else {
                erroMsg = "update error";
            }
        }

        if (erroMsg.isEmpty()) {
            if (insertBean.isNeedRebuildAuth()) {
                //step 4:添加共享权限
                ModeRightInfo ModeRightInfo = new ModeRightInfo();
                ModeRightInfo.setNewRight(true);
                ModeRightInfo.editModeDataShare(creator, insertBean.getModuleId(), billid);
            } else {
                log.warn("no need for build module data auth,billid:" + billid);
            }
        } else {
            //step 5:出错删除已创建的数据
            log.info("update generated moudle data error:：" + erroMsg);
            //将刚生成的建模数据删除
            delDataById(insertBean.getTableName(), billid);
            //将billid置为错误的-1
            billid = -1;
        }
        //step 6:返回信息
        result.setSuccess(erroMsg.isEmpty()).setErroMsg(erroMsg);
        result.setBillid(billid);
        result.setErroMsg(erroMsg);
        String endTime = TimeUtil.getCurrentTimeString();
        //计算开始到结束时间相隔的秒数
        long takeTime = TimeUtil.timeInterval(beginTime, endTime);
        log.info("doInsertDataOne---END time:" + endTime + ",it takes--" + takeTime + "--second");
        return result;
    }

    /**
     * 实际执行更新建模主表数据(批量)
     *
     * @param updateBean
     * @return
     */
    @SuppressWarnings("unchecked")
    private static String doUpdateData(ModuleUpdateBean updateBean) {
        String erroMsg = "";
        String sql;
        String beginTime = TimeUtil.getCurrentTimeString();
        log.info("doUpdateData--START time:" + beginTime);

        String eachField;
        //所有记录使用记录SQL操作的参数集合
        List paramList;
        //每一个记录使用记录SQL操作的参数集合
        List<Object> eachParam;
        //每一个记录使用记录SQL操作的参数集合
        StringBuilder sbUpdate = new StringBuilder();
        //step 1: 拼接update语句，=值用?占位
        List<String> insertFields = updateBean.getFields();
        for (int i = 0; i < insertFields.size(); i++) {
            eachField = insertFields.get(i);
            //排除id字段
            if (!eachField.equalsIgnoreCase("id")) {
                sbUpdate.append(eachField).append("=").append("?");
                if (i < insertFields.size() - 1) {
                    sbUpdate.append(", ");
                }
            }
        }

        log.info("sbUpdate：" + sbUpdate);
        //step 3 : 定义更新数据的sql
        sql = "update " + updateBean.getTableName() + " set " + sbUpdate + " where id= ?";
        log.info("update module sql:" + sql);

        //step 4 : 处理列表值，按批次处理，1000条一批
        List<List<Object>> values = updateBean.getValues();
        // 原始列表的长度
        int totalSize = values.size();
        log.info("totalSize :" + totalSize);
        // 分批处理的数量
        int numBatches = (int) Math.ceil((double) totalSize / batchSize);
        log.info("batch number size:" + numBatches);

        // 获取数据库类型
        RecordSet rs = new RecordSet();
        String dbType = rs.getDBType();

        //step 4 :遍历批次
        for (int i = 0; i < numBatches; i++) {
            // 计算每个批次的起始索引和结束索引
            int startIndex = i * batchSize;
            int endIndex = Math.min((i + 1) * batchSize, totalSize);
            // 获取当前批次的value列表
            List<List<Object>> batchList = values.subList(startIndex, endIndex);
            log.info("current batch :" + i + ",batchList size ：" + batchList.size());
            //每个批次重置参数列表
            paramList = new ArrayList<>();
            // step 5 : 添加建模主表必要的字段值
            for (List<Object> eachBatch : batchList) {
                eachParam = new ArrayList<>(eachBatch);
                //将最后一个元素（id值）删除
                Object lastObj = eachParam.remove(eachParam.size() - 1);
                // 转换字段类型并添加到参数列表
                for (int j = 0; j < eachParam.size(); j++) {
                    Object convertedValue = convertValueByFieldType(eachParam.get(j), insertFields.get(j), updateBean.getTableName(), dbType);
                    eachParam.set(j, convertedValue);
                }
                //加回id值元素，保证id值在最后一位
                eachParam.add(lastObj);
                paramList.add(eachParam);
            }
            log.info("batch update first :" + paramList.get(0));

            //step 6: 执行sql，批量更新数据
            if (!rs.executeBatchSql(sql, paramList)) {
                erroMsg = "current batch :" + i + "，batch update sql error:";
                if (!updateBean.isE8()) {
                    erroMsg += rs.getExceptionMsg();
                }
                log.info(erroMsg);
            }
        }
        String endTime = TimeUtil.getCurrentTimeString();
        //计算开始到结束时间相隔的秒数
        long takeTime = TimeUtil.timeInterval(beginTime, endTime);
        log.info("doUpdateData---END time:" + endTime + ",it takes--" + takeTime + "--second");
        return erroMsg;
    }

    /**
     * 获取当前建模数据使用的线程池
     *
     * @return
     */
    private static ExecutorService getExecutor() {
        ExecutorService exeService;
        //判断是否有缓存的线程池
        if (!ThreadPoolUtil.cachePoolExits(SDThreadPoolCst.MODULE_DATA_THREAD_NAME)) {
            //IO 密集型任务，线程池大小一般设置为 CPU 核心数乘以一个系数，常见的系数是 2 或者 2.5。这里主要是数据库交互
            int poolSize = Runtime.getRuntime().availableProcessors() * SDThreadPoolCst.POOL_SIZE_RATIO;
            log.info("generate pool size: " + poolSize);
            exeService = ThreadPoolUtil.getThreadPool(SDThreadPoolCst.MODULE_DATA_THREAD_NAME, String.valueOf(poolSize));
        } else {
            exeService = ThreadPoolUtil.getCachePool(SDThreadPoolCst.MODULE_DATA_THREAD_NAME);
        }
        return exeService;
    }

    /**
     * 校验列表中是否包含某个值，忽略大小写
     *
     * @param list
     * @param matchStr
     * @return
     */
    public static boolean checkContainsStr(List<String> list, String matchStr) {
        return list.stream().anyMatch(str -> str.equalsIgnoreCase(matchStr));
    }

    /**
     * 重构建模数据权限
     * 比如用RecordSet更新某条数据的字段值，并且这个字段值用于了数据权限的条件，那么在更新了值之后就需要重构权限
     *
     * @param creator
     * @param moduleId
     * @param billid
     */
    public static void resetShare(int creator, int moduleId, int billid) {
        //编辑时重新生成默认共享
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.rebuildModeDataShareByEdit(creator, moduleId, billid);
    }

    /**
     * 重构建模数据权限
     * 比如用RecordSet更新某条数据的字段值，并且这个字段值用于了数据权限的条件，那么在更新了值之后就需要重构权限
     *
     * @param moduleId
     * @param billid
     */
    public static void resetShare(int moduleId, int billid) {
        //编辑时重新生成默认共享
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        //这里创建人传-1，源码里会判断传的是小于0，会重新取建模的modedatacreater创建人的
        ModeRightInfo.rebuildModeDataShareByEdit(-1, moduleId, billid);
    }

    /**
     * 根据建模表名获取建模id
     * 可能一个建模表名对应多个建模id，只返回第一个建模id
     *
     * @param tableName
     * @return
     */
    public static int getModuleIdByName(String tableName) {
        int moduleId = -1;
        if (!tableName.isEmpty()) {
            RecordSet rs = new RecordSet();
            rs.executeQuery("select b.tablename,a.id from modeinfo a " +
                    "left join workflow_bill b on (a.formid= b.id) where b.tablename = ?", tableName);
            if (rs.next()) {
                moduleId = rs.getInt("id");

            }
        }
        return moduleId;
    }

    /**
     * 根据建模id获取建模表名
     *
     * @param moduleId 建模id
     * @return
     */
    public static String getModuleNameById(String moduleId) {
        String tableName = "";
        if (!moduleId.isEmpty()) {
            RecordSet rs = new RecordSet();
            rs.executeQuery("select b.tablename,a.id from modeinfo a " +
                    "left join workflow_bill b on (a.formid= b.id) where a.id = ?", moduleId);
            if (rs.next()) {
                tableName = Util.null2String(rs.getString("tablename"));
            }
        }
        return tableName;
    }

    /**
     * 删除表数据
     *
     * @param table  建模表名
     * @param billid 建模数据id
     * @return
     */
    public static void delDataById(String table, int billid) {
        if (!table.isEmpty() && billid > 0) {
            RecordSet rs = new RecordSet();
            rs.executeUpdate("delete from " + table + " where id = ?", billid);
        }
    }

    /**
     * 删除表数据
     *
     * @param table  建模表名
     * @param idList 建模数据id list
     * @return
     */
    public static void delDataByIdList(String table, List<Integer> idList) {
        if (!table.isEmpty() && !idList.isEmpty()) {
            RecordSet rs = new RecordSet();
            StringBuilder sb = new StringBuilder();
            sb.append("delete from ").append(table).append(" where 1=1 ");
            sb.append(" and ( ");
            for (int i = 0; i < idList.size(); i++) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append(" (id = ").append(idList.get(i)).append(") ");
            }
            sb.append("  ) ");
            log.info("delDataByIdList sql:" + sb);
            rs.executeUpdate(sb.toString());
        }
    }

    /**
     * 查询建模的一条数据
     *
     * @param tableName
     * @param billid
     * @return
     */
    public static JSONObject getOneData(String tableName, String billid) {
        JSONObject result = new JSONObject();
        if (!tableName.isEmpty() && !billid.isEmpty()) {
            RecordSet rs = new RecordSet();
            if (rs.executeQuery("select * from " + tableName + " where id = ?", billid)) {
                JSONArray ja = QueryUtil.getJSONListAllField(rs);
                if (!ja.isEmpty()) {
                    result = ja.getJSONObject(0);
                }
            }
        }
        return result;

    }

    /**
     * 查询建模的一条数据，返回Map对象
     *
     * @param tableName 建模主表名
     * @param billid    主表的建模数据id
     * @return
     */
    public static Map<String, Object> getMapData(String tableName, String billid) {
        Map<String, Object> result = new HashMap<>();
        if (!tableName.isEmpty() && !billid.isEmpty()) {
            RecordSet rs = new RecordSet();
            if (rs.executeQuery("select * from " + tableName + " where id = ?", billid)) {
                List<Map<String, Object>> list = QueryUtil.getMapList(rs);
                if (!list.isEmpty()) {
                    result = list.get(0);
                }
            }
        }
        return result;
    }

    /**
     * 查询某一条建模数据的所有明细表数据
     *
     * @param tableName   建模主表名
     * @param billid      主表的建模数据id
     * @param detailIndex 明细的index 1,2,3...
     * @return
     */
    public static List<Map<String, Object>> getDetailMapData(String tableName, String billid, int detailIndex) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (!tableName.isEmpty() && !billid.isEmpty() && detailIndex > 0) {
            String detailTableName = tableName + "_dt" + detailIndex;
            RecordSet rs = new RecordSet();
            if (rs.executeQuery("select d.* from " + detailTableName + " d " +
                    " inner join " + tableName + " m on (d.mainid = m.id) " +
                    " where m.id = ?", billid)) {
                result = QueryUtil.getMapList(rs);
            }
        }
        return result;
    }

    /**
     * 插入建模日志
     *
     * @param list
     * @param moduleId
     * @return
     */
    public static ModuleResult insertModuleLog(List<Modeviewlog> list, int moduleId) {
        String fieldName;
        Object fieldValue;
        String errorMsg = "";
        String insertFields = "";

        List values = new ArrayList<>();
        List<Object> value;
        StringBuilder sb = new StringBuilder();

        ModuleResult mr = new ModuleResult();
        mr.setSuccess(true);
        try {
            String modeViewLogName = "Modeviewlog_" + moduleId;
            Class<?> clazz = Modeviewlog.class;
            Field[] fields = clazz.getDeclaredFields();

            //设置插入字段列表
            for (Field field : fields) {
                field.setAccessible(true); // 设置可访问私有字段
                //字段名
                fieldName = field.getName();
                sb.append(fieldName).append(CommonCst.COMMA_EN);
            }
            if (!sb.toString().isEmpty()) {
                insertFields = sb.substring(0, sb.length() - 1);
            }
            //设置插入sql
            String sqlInsert = "insert into  " + modeViewLogName + "(" + insertFields + ") VALUES (?,?,?,?,?,?,?,?)";
            log.info("log sqlInsert :" + sqlInsert);

            //设置参数值列表
            for (Modeviewlog log : list) {
                value = new ArrayList<>();
                for (Field field : fields) {
                    field.setAccessible(true); // 设置可访问私有字段
                    //获取字段值
                    fieldValue = field.get(log);
                    value.add(fieldValue);
                }
                values.add(value);
            }

            //插入操作日志表
            RecordSet rs = new RecordSet();
            log.info("log sqlInsert logValues size:" + values.size());
            if (!rs.executeBatchSql(sqlInsert, values)) {
                errorMsg = "sqlInsert batch insert log error:" + rs.getExceptionMsg() + ";" + rs.getMsg();
            }
        } catch (Exception e) {
            errorMsg = "insertModuleLog异常：" + SDUtil.getExceptionDetail(e);
        }
        if (!errorMsg.isEmpty()) {
            log.info(errorMsg);
            mr.setSuccess(false);
            mr.setErroMsg(errorMsg);
        }
        return mr;

    }

    /**
     * 插入一条对应的建模操作日志
     *
     * @param moduleId    建模模块id
     * @param billid      建模数据id
     * @param operateType 操作类型 1：新建 2：修改 3：删除 4：查看
     * @param operateDesc 操作内容
     * @param operator    操作人
     * @return
     */
    public static ModuleResult insertOneModuleLog(int moduleId, int billid, int operateType, String operateDesc, int operator) {
        List<Modeviewlog> modeviewlogList = new ArrayList<>();
        Modeviewlog modeviewlog = new Modeviewlog();
        modeviewlog.setRelatedid(billid);
        modeviewlog.setRelatedname("-");
        modeviewlog.setOperatetype(operateType);
        modeviewlog.setOperatedesc(operateDesc);
        modeviewlog.setOperateuserid(operator);
        modeviewlog.setOperatedate(TimeUtil.getToday());
        modeviewlog.setOperatetime(TimeUtil.getOnlyCurrentTimeString());
        modeviewlog.setClientaddress("");
        return insertModuleLog(modeviewlogList, moduleId);
    }

    /**
     * 插入一条对应的建模的更新操作日志，默认为更新操作
     *
     * @param moduleId    建模模块id
     * @param billid      建模数据id
     * @param operateDesc 操作内容
     * @param operator    操作人
     * @return
     */
    public static ModuleResult insertOneModuleUpdateLog(int moduleId, int billid, String operateDesc, int operator) {
        return insertOneModuleLog(moduleId, billid, 2, operateDesc, operator);
    }

    /**
     * 批量刷新指定建模id的所有的建模权限
     *
     * @param moduleId
     */
    public static void resetModShare(int moduleId) {
        SDModuleRightThread rightThread = new SDModuleRightThread();
        rightThread.setModeId(moduleId);
        rightThread.resetModeRight();
    }

    /**
     * 批量刷新指定建模id和数据id列表的的建模权限
     * KB900210400之前版本，不可以使用，需使用一条一条重构的方法
     *
     * @param moduleId
     * @param billIdList
     */
    public static void resetModShare(int moduleId, List<String> billIdList) {
        if (billIdList != null && !billIdList.isEmpty()) {
            SDModuleRightThread rightThread = new SDModuleRightThread();
            rightThread.setModeId(moduleId);
            rightThread.setBillIdList(billIdList);
            rightThread.resetModeRight();
        } else {
            log.error("moduleId:" + moduleId + ",指定的billid列表为空，不执行重构权限");
        }
    }

    /**
     * 刷新指定建模id的所有的建模权限
     * 一条一条重构
     * KB900210400之前版本，需要一条一条重构
     *
     * @param moduleId
     */
    public static void resetModShareOneByOne(int moduleId) {
        SDModuleRightThread rightThread = new SDModuleRightThread();
        rightThread.setModeId(moduleId);
        rightThread.setNeedOneByOne(true);
        rightThread.resetModeRight();
    }

    /**
     * 刷新指定建模id和数据id列表的的建模权限
     * 一条一条重构
     * KB900210400之前版本，需要一条一条重构
     *
     * @param moduleId
     * @param billIdList
     */
    public static void resetModShareOneByOne(int moduleId, List<String> billIdList) {
        if (billIdList != null && !billIdList.isEmpty()) {
            SDModuleRightThread rightThread = new SDModuleRightThread();
            rightThread.setModeId(moduleId);
            rightThread.setBillIdList(billIdList);
            rightThread.setNeedOneByOne(true);
            rightThread.resetModeRight();
        } else {
            log.error("moduleId:" + moduleId + ",指定的billid列表为空，不执行重构权限");
        }
    }

    /**
     * 异步插入建模数据(批量数据)
     * 默认一个建模表只有一个建模id
     *
     * @param list
     * @param moduleTableName
     * @param clazz
     * @param creator
     * @param <T>
     */
    public static <T> void insertAc(List<T> list, String moduleTableName, int creator, Class<T> clazz) {
        List<String> insertFields = new ArrayList<>();
        Object fieldValue;
        List<List<Object>> values = new ArrayList<>();
        List<Object> eachValue;
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                insertFields.add(field.getName());
            }
            for (T eachData : list) {
                eachValue = new ArrayList<>();
                for (Field field : fields) {
                    // 设置可访问私有字段
                    field.setAccessible(true);
                    // 获取字段值
                    fieldValue = field.get(eachData);
                    eachValue.add(fieldValue);
                }
                values.add(eachValue);
            }
            int moduleId = getModuleIdByName(moduleTableName);
            ModuleInsertBean insertBean = new ModuleInsertBean();
            insertBean.setTableName(moduleTableName)
                    .setFields(insertFields)
                    .setValues(values)
                    .setCreatorId(creator)
                    .setModuleId(moduleId);
            ModuleDataUtil.insertAc(insertBean);
        } catch (Exception e) {
            log.info("doInsert2Module 异常：" + SDUtil.getExceptionDetail(e));
        }
    }

    /**
     * 获取建模模块默认的附件目录id
     * 没配置查不到返回-1
     *
     * @param moduleId
     * @return
     */
    public static int getDefaultSecCatId(int moduleId) {
        String sql = "select seccategory from modeinfo where id = " + moduleId;
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return rs.getInt("seccategory");
            }
        }
        return -1;
    }

    /**
     * 获取在某创建时间的新增数据id列表
     *
     * @param createDate
     * @param createTime
     * @param moduleTableName
     * @return
     */
    private static List<String> getNewBillIds(String createDate, String createTime, String moduleTableName) {
        List<String> result = new ArrayList<>();
        String sql = "select id from " + moduleTableName + " where 1=1 ";
        sql += " and modedatacreatedate = '" + createDate + "' ";
        sql += " and modedatacreatetime = '" + createTime + "' ";
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.add(Util.null2String(rs.getString("id")));
            }
        } else {
            log.error("getNewBillIds error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return result;
    }

    /**
     * 根据数据库类型和字段类型转换值
     *
     * @param value     原始值
     * @param fieldName 字段名
     * @param tableName 表名
     * @param dbType    数据库类型
     * @return 转换后的值
     */
    private static Object convertValueByFieldType(Object value, String fieldName, String tableName, String dbType) {
        if (value == null) {
            return null;
        }

        try {
            String dataType = getFieldType(tableName, fieldName, dbType);
            if (dataType == null) {
                return value;
            }

            try {
                if (Util.null2String(value).isEmpty()) {
                    return null;
                }
                switch (dataType) {
                    case "int":
                    case "integer":
                        return Integer.parseInt(value.toString());
                    case "number":
                    case "numeric":
                        return new BigDecimal(value.toString()).intValue();
                    case "decimal":
                    case "float":
                    case "double":
                    case "real":
                        return new BigDecimal(value.toString());
                    case "date":
                        return DateHelper.parseDate(value.toString());
                    case "datetime":
                    case "timestamp":
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            return sdf.parse(value.toString());
                        } catch (Exception e) {
                            log.error("Parse datetime error for value: " + value, e);
                            return value;
                        }
                    case "boolean":
                    case "bit":
                        return Boolean.parseBoolean(value.toString());
                    default:
                        return value.toString();
                }
            } catch (Exception e) {
                log.error("Convert value error for field " + fieldName + ", value: " + value + ", type: " + dataType, e);
                return value;
            }
        } catch (Exception e) {
            log.error("Error in convertValueByFieldType for field " + fieldName + ", value: " + value + ", table: " + tableName, e);
        }

        return value;
    }

    /**
     * 字段类型缓存信息
     */
    private static class FieldTypeCache {
        private final String dataType;
        private final long expireTime;

        public FieldTypeCache(String dataType) {
            this.dataType = dataType;
            // 设置30分钟过期
            this.expireTime = System.currentTimeMillis() + 30 * 60 * 1000;
        }

        public String getDataType() {
            return dataType;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }
}
