package com.engine.sd2.module.modquery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.general.Util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 建模查询工具类
 */
public class ModQueryUtil {
    private static final String HTTP_URL = "http://127.0.0.1";
    private static final String API_GETLIST = "/api/cube/search/getList";
    private static final String API_DATAS = "/api/ec/dev/table/datas";
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(ModQueryUtil.class);

    @Getter
    @Setter
    public static class ModQueryDataResult {
        /**
         * 最新的datakey
         */
        private String dataKey;
        /**
         * 查询结果的列表数据
         */
        private List<Map<String, Object>> list;
    }

    /**
     * 获取第一页数据集合
     * （可以在建模查询里设置分页数量较大的数量，这样可以查一次查完）
     *
     * @param port        当前内部服务器的端口
     * @param customid    建模查询id
     * @param cookie      cookie
     * @param queryParams 查询参数
     * @param pageNo      页码（从1开始）
     * @return
     */
    public static ModQueryDataResult getPageDataListWithDatakey(String port, String customid, String cookie, Map<String, Object> queryParams, int pageNo, String dataKey) {
        ModQueryDataResult result = new ModQueryDataResult();
        List<Map<String, Object>> list = null;
        try {
            if (StringUtils.isNotBlank(dataKey)) {
                Map<String, Object> myParams = new HashMap<>();
                myParams.put("dataKey", dataKey);
                myParams.put("current", pageNo);//当前页码
                log.info("customid:" + customid + " getPageDataListWithDatakey myParams: " + myParams);
                //调用本机环境的接口
                String url = HTTP_URL + ":" + port + API_DATAS;
                log.info("customid:" + customid + "getPageDataListWithDatakey url :" + url);
                String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);
                //检查datakey是否还是有效的
                if (isDataKeyInvalid(postResult)) {
                    list = parseDatasResult2List(postResult);
                } else {
                    log.info("customid:" + customid + " dataKey失效，重新获取datakey");
                    //重新获取一次datakey
                    dataKey = getDataKey(port, customid, cookie, queryParams);
                    //返回根据最新datakey查询的数据
                    list = getDataListByDataKeyAndPageNo(port, cookie, pageNo, dataKey);
                }
            } else {
                log.error("customid:" + customid + " dataKey为空");
            }
        } catch (Exception e) {
            log.error("customid:" + customid + " getPageDataListWithDatakey 异常", e);
        }
        result.setDataKey(dataKey);
        result.setList(list);
        return result;
    }

    /**
     *
     * 获取建模查询数据，根据datakey和页码
     *
     * @param port   当前内部服务器的端口
     * @param cookie cookie
     * @param pageNo 页码（从1开始）
     * @return
     */
    public static List<Map<String, Object>> getDataListByDataKeyAndPageNo(String port, String cookie, int pageNo, String dataKey) {
        try {
            Map<String, Object> myParams = new HashMap<>();
            myParams.put("dataKey", dataKey);
            myParams.put("current", pageNo);//当前页
            log.info("getDataList myParams: " + myParams);
            //调用本机环境的接口
            String url = HTTP_URL + ":" + port + API_DATAS;
            log.info("getFirstDataList url :" + url);
            String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);
            return parseDatasResult2List(postResult);

        } catch (Exception e) {
            log.error("getDataList 异常", e);
        }
        return null;
    }

    /**
     * 获取第一页数据集合
     * （可以在建模查询里设置分页数量较大的数量，这样可以查一次查完）
     *
     * @param port        当前内部服务器的端口
     * @param customid    建模查询id
     * @param cookie      cookie
     * @param queryParams 查询参数
     * @return
     */
    public static List<Map<String, Object>> getFirstDataList(String port, String customid, String cookie, Map<String, Object> queryParams) {
        try {
            //获取sesionkey
            String dataKey = getDataKey(port, customid, cookie, queryParams);
            if (StringUtils.isNotBlank(dataKey)) {
                Map<String, Object> myParams = new HashMap<>();
                myParams.put("dataKey", dataKey);
                myParams.put("current", "1");//当前页
                log.info("getDataList myParams: " + myParams);
                //调用本机环境的接口
                String url = HTTP_URL + ":" + port + API_DATAS;
                log.info("getFirstDataList url :" + url);
                //调用泛微接口
                String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);
                //转换为list
                return parseDatasResult2List(postResult);
            }
        } catch (Exception e) {
            log.error("getDataList 异常", e);
        }
        return null;
    }

    /**
     * 请求data key
     *
     * @param port        当前内部服务器的端口
     * @param customid    建模查询id
     * @param cookie      cookie
     * @param queryParams 查询参数
     * @return
     */
    public static String getDataKey(String port, String customid, String cookie, Map<String, Object> queryParams) {
        String datakey = "";
        try {
            Map<String, Object> myParams = new HashMap<>();
            if (queryParams != null && !queryParams.isEmpty()) {
                myParams.putAll(queryParams);
            }
            //额外加上固定参数
            myParams.put("customid", customid);//建模查询id
            myParams.put("guid", "search");
            myParams.put("isQuickSearch", "1");
            myParams.put("groupValue", "all");
            log.info("getDataKey myParams: " + myParams);
            String url = HTTP_URL + ":" + port + API_GETLIST;
            log.info("getDataKey url : " + url);
            String postResult = HttpUtil.postWeaverDataWithCookie(url, cookie, myParams);
            log.info("getDataKey result: " + postResult);
            if (StringUtils.isNotBlank(postResult)) {
                JSONObject jsonObject = JSON.parseObject(postResult);
                if (jsonObject != null && jsonObject.containsKey("datas")) {
                    datakey = Util.null2String(jsonObject.get("datas"));
                }
            }
        } catch (Exception e) {
            log.error("getDataKey 异常", e);
        }
        return datakey;
    }

    /**
     * 判断建模查询的datas数据是否失效
     *
     * @param apiDatasResult
     * @return
     */
    private static boolean isDataKeyInvalid(String apiDatasResult) {
        if (StringUtils.isBlank(apiDatasResult)) {
            return false;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(apiDatasResult);
            if (jsonObject != null) {
                return jsonObject.containsKey("status") && !(boolean) jsonObject.get("status") && "005".equals(Util.null2String(jsonObject.get("errorCode")));
            }
        } catch (Exception e) {
            log.error("isDataKeyInvalid 异常", e);
        }
        return false;
    }


    /**
     * 解析建模查询的datas数据
     *
     * @param apiDatasResult
     * @return
     */
    private static List<Map<String, Object>> parseDatasResult2List(String apiDatasResult) {
        try {
            if (StringUtils.isBlank(apiDatasResult)) {
                return null;
            }
            JSONObject jsonObject = JSON.parseObject(apiDatasResult);
            if (jsonObject != null && jsonObject.containsKey("datas")) {
                JSONArray jsonArray = jsonObject.getJSONArray("datas");
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    log.info("parseDatasResult2List result data size: " + jsonArray.size());
                    String jsonStr = jsonArray.toJSONString();
                    return JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {
                    });
                }
            }
        } catch (Exception e) {
            log.error("parseDatasResult2List 异常", e);
        }
        return null;
    }


}
